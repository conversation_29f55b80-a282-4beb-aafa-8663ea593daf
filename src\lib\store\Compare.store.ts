import { create } from "zustand";
import { persist } from "zustand/middleware";
import { sessionStoragePersistStorage } from "./cp.store";


//比较的商品id
type State = {
  compareIds: number[],
  // 是否显示
  show:boolean,
  changeShow: (bool: boolean) => void
  removeCompareProduct: (id: any) => void; // 删除指定商品的函数
  setcCmpareProducts: (id: any) => void,
  removeAllCompare: () => void;
}
export const useCompareStore = create(
  persist<State>(
    (set, get) => ({
      compareIds: [],
      show:false,
      changeShow:(bool) => {
        set({ show:bool });
      },
      setcCmpareProducts: (id) => {
        const compareIds = get().compareIds;

        // 如果 ID 已存在，直接移除它（不重新添加）
        if (compareIds.includes(id)) {
          const updatedIds = compareIds.filter((compareId) => compareId !== id);
          set({
            compareIds: updatedIds,
          });
          return;
        }

        // 如果 ID 不存在，添加到数组末尾
        const updatedIds = [...compareIds, id];

        // 限制数组长度最多为 2（根据需求调整）
        if (updatedIds.length > 2) {
          updatedIds.shift(); // 移除第一个元素
        }

        // 更新状态
        set({
          compareIds: updatedIds,
        });
      },
      
          // 根据 ID 删除指定的商品
      removeCompareProduct: (id) => {
        const compareIds = get().compareIds;

        // 过滤掉需要删除的 ID
        const updatedIds = compareIds.filter((compareId) => compareId !== id);

        // 更新状态
        set({
          compareIds: updatedIds,
        });
      },

      removeAllCompare: () => {
        set({ compareIds: [] });
      }
    }),
    {
      name: "compare-store",
      storage: sessionStoragePersistStorage
    }
  )
);

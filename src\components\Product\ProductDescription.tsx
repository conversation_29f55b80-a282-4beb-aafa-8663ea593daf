"use client";
import { defaultLocale } from "@/config";
import { Tabs } from "antd";
import { StyledArticle } from "../Blogs/BlogArticle";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { contactInfo } from "@/lib/contacts";
import { motion } from "framer-motion";
import { containerVariants, itemVariants } from "@/lib/utils/util";
import ProductReviews from "./product-reviews";
const ProductDescription = ({ product, locale }) => {
	const t = useTranslations("nav");
	return (
		<div>
			<h2 className="text-center text-4xl mb-8 irs">Product Details</h2>
			<StyledArticle>
				<div
					className="p-4"
					dangerouslySetInnerHTML={{
						__html:
							locale == defaultLocale
								? filterSortDsc(product.descriptionJson).longDsc
								: filterSortDsc(product.translation.descriptionJson).longDsc || filterSortDsc(product.descriptionJson).longDsc,
					}}
				/>
			</StyledArticle>
			<h2 className="text-center text-4xl mt-20 mb-8 irs">Reviews</h2>
			<ProductReviews productId={product.id} rating={product?.rating || 5} />
		</div>
	)
};

export default ProductDescription;

type dscList = {
	blocks: dscObj[];
};
type dscObj = {
	data: { text: string };
	type: string;
};
const filterSortDsc = (dsc: string) => {
	if (!dsc) {
		dsc = '{"blocks": []}';
	}

	const dscObj = JSON.parse(dsc) as dscList;
	let sortDsc = "";
	let longDsc = "";

	// 打印后立即检查blocks是否存在
	// console.log(dscObj, 'dscObj');

	// 首先检查blocks是否存在
	if (!dscObj.blocks) {
		// 如果blocks不存在，初始化为空数组
		dscObj.blocks = [];
	}

	// 现在可以安全地访问length属性
	if (dscObj.blocks.length > 0) {
		// 检查索引1是否存在
		if (dscObj.blocks.length > 1 && dscObj.blocks[1]?.data?.text) {
			sortDsc = dscObj.blocks[1].data.text;
		}

		// 检查索引0是否存在
		if (dscObj.blocks[0]?.data?.text) {
			longDsc = handlerInnerHtml(dscObj.blocks[0].data.text);
		}
	}

	return { sortDsc, longDsc };
};
const handlerInnerHtml = (html: string): string => {
	if (!html) return html;
	return html
		.replace(/&lt;/g, "<")
		.replace(/&gt;/g, ">")
		.replace(/&amp;/g, "&")
		.replace(/&quot;/g, '"')
		.replace(/&#8216;/g, "‘")
		.replace(/&#8217;/g, "’")
		.replace(/&#8211;/g, "–")
		.replace(/&nbsp;/g, " ");
};

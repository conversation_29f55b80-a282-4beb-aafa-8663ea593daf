"use client";
import { useCompareStore } from "@/lib/store/Compare.store";
import { Drawer, Modal } from "antd";
import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { fetchData } from "../Collection";
import { RingLoader } from "react-spinners";
import { ProductListItemFragment } from "@/gql/graphql";
import { filterCateImg } from "../Product/product-card";
import { useRouter } from "@/navigation";
import useIsMobile from "@/lib/hooks/useIsMobile";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { Link } from "@/navigation";
import { defaultLocale } from "@/config";
import { useProductStore } from "@/lib/store/product.store";
import CompareModal from "./CompareModal";
function Index() {
	let { show, changeShow } = useCompareStore();

	const [CompareList, setCompareList] = useState([]);
	const [showCompareModal, setShowCompareModal] = useState(false);
	const t = useTranslations();
	const { compareIds, removeAllCompare, removeCompareProduct } = useCompareStore();
	const [loading, setLoading] = useState(false);
	const locale = useLocale();

	let isMobile = useIsMobile()
	let router = useRouter()
	useEffect(() => {
		// if (show) {
		// 	return;
		// }

		if (!compareIds.length || !locale) return setCompareList([]);
		fetchData(compareIds, setLoading, locale).then((res) => {

			// 按照 compareIds 顺序排序
			const sortedData = compareIds
				.map((id) => res.find((item) => item.node.id === id))
				.filter(Boolean); // 去掉可能未匹配到的数据

			// 更新排序后的结果
			setCompareList(sortedData);
		});
	}, [show, compareIds]);

	function removeId(id) {
		console.log(compareIds.length);

		removeCompareProduct(id)

		if (compareIds.length == 1) {
			setTimeout(() => {
				changeShow(false)
			}, 500)

		}
	}


	return (
		<Drawer
			placement={"bottom"}
			closable={false}
			onClose={() => changeShow(false)}
			open={show}
			height={isMobile ? '55%' : "35%"}
			className="compare-drawer"
		// mask={false}
		// rootClassName="!hidden md:!block"
		>
			<div className="box-border flex h-full   w-full flex-col">
				{/* <div className="w-full text-right">
					{" "}
					<i className="ri-close-fill cursor-pointer text-3xl" onClick={() => changeShow(false)}></i>
				</div> */}
				<div className="flex flex-1 max-md:flex-col relative">
					{/* 左侧标题 - 绝对定位 */}
					<div className="absolute left-0 top-0 h-full flex items-center z-10 max-md:relative max-md:h-auto max-md:justify-center max-md:py-2">
						<div className="text-xl box-border ib text-black flex items-center gap-x-2 px-3 py-2">
							{t('nav.Compare1')} <span className="max-lg:hidden">{t('common.Products')}</span> ({compareIds.length}/2)
						</div>
					</div>

					{/* 右侧按钮 - 绝对定位 */}
					<div className="absolute right-0 top-0 h-full flex items-center z-10 max-md:hidden">
						<div className="flex items-center justify-center gap-4 px-3">
							<button
								onClick={() => {
									if (compareIds.length === 2) {
										setShowCompareModal(true);
									}
								}}
								disabled={compareIds.length < 2}
								className="box-border flex hover:text-white cursor-pointer items-center justify-center gap-x-2 rounded-full bg-[#80bd02] hover:bg-opacity-80 px-5 py-2 text-[18px] text-white transition-colors irs !font-normal disabled:opacity-50 disabled:cursor-not-allowed"
							>
								{t('nav.Compare1')}
							</button>
							<span className="underline cursor-pointer text-black ib hover:text-opacity-80 transition-colors text-[14px]" onClick={() => {
								removeAllCompare()
								changeShow(false)
							}}>	{t("common.2404e3fb11a4cd4619b87d75ba56e345d8b0")}</span>
						</div>
					</div>

					{/* 中间产品区域 - 真正的全宽居中 */}
					<div className="w-full h-full flex items-center justify-center max-md:flex-1 max-md:py-2">
						<div className="flex items-center gap-x-6 max-md:gap-x-3">
							{/* 始终显示两个位置 */}
							{[0, 1].map((index) => {
								const item = CompareList[index];
								if (item) {
									return <CompareItem key={item.node.id} delFn={removeId} productItem={item.node as ProductListItemFragment} />;
								} else {
									return (
										<div key={`empty-${index}`} className="flex-shrink-0">
											<div className="flex flex-col items-center">
												{/* 空的产品卡片 */}
												<div className="relative w-[180px] h-[220px] max-md:w-[130px] max-md:h-[160px] rounded-lg border-2 border-black bg-[#f4f4f4] flex flex-col items-center justify-center mb-2">
													{/* <i className="ri-add-line text-3xl text-gray-300 mb-1"></i> */}
													<span className="text-xs max-md:text-[10px] text-gray-400 text-center px-3 max-md:px-2 leading-tight">
														{t('common.Add')} {t('common.Product')} <br />
														{t("common.tocompare")}
													</span>
												</div>
												{/* 空的标题区域 - 与有产品的标题区域保持相同尺寸 */}
												<div className="w-[180px] max-md:w-[130px] text-center h-[40px] max-md:h-[30px] flex items-center justify-center">
													<div className="w-full">
														<div className="h-3 max-md:h-2 rounded mb-1"></div>
														<div className="h-3 max-md:h-2 rounded w-3/4 mx-auto"></div>
													</div>
												</div>
											</div>
										</div>
									);
								}
							})}
						</div>
					</div>

					{/* 移动端按钮 */}
					<div className="flex items-center justify-between px-4 py-3 md:hidden">
						<span className="underline cursor-pointer text-black ib hover:text-opacity-80 transition-colors text-[14px]" onClick={() => {
							removeAllCompare()
							changeShow(false)
						}}>	{t("common.2404e3fb11a4cd4619b87d75ba56e345d8b0")}</span>
						<button
							onClick={() => {
								if (compareIds.length === 2) {
									setShowCompareModal(true);
								}
							}}
							disabled={compareIds.length < 2}
							className="box-border flex  hover:text-white cursor-pointer items-center justify-center gap-x-2 rounded-full bg-[#80bd02] hover:bg-opacity-80 px-4 py-2 text-base text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
						>
							{t('nav.Compare1')}
						</button>
					</div>
				</div>
			</div>

			{/* 比较模态框 */}
			<CompareModal
				open={showCompareModal}
				onClose={() => setShowCompareModal(false)}
				compareList={CompareList}
				onRemoveProduct={removeId}
			/>
		</Drawer>
	);
}
function CompareItem({ productItem, delFn }: { productItem: ProductListItemFragment, delFn: Function }) {
	return (
		<div className="group relative flex-shrink-0 max-md:snap-center">
			<div className="flex flex-col items-center">
				{/* Product card - 与空占位符保持相同尺寸 */}
				<div className="relative">
					<div className="relative overflow-hidden w-[180px] h-[220px] max-md:w-[130px] max-md:h-[160px] rounded-lg border-2 border-black bg-[#f4f4f4] transition-all duration-200 hover:shadow-lg mb-2">
						{/* Product image container */}
						<div className="flex items-center justify-center h-full overflow-hidden">
							<SEOOptimizedImage
								src={filterCateImg(productItem.metadata)[0]?.url || "/image/default-image.webp"}
								alt={productItem.name}
								width={1000}
								height={1000}
								quality={100}
								className="w-full h-full object-cover"
							/>
						</div>
					</div>
					{/* Remove button */}
					<button
						onClick={() => delFn(productItem.id)}
						className="absolute -top-2 -right-2 flex h-9 w-9 max-md:h-7 max-md:w-7 items-center justify-center rounded-full bg-white text-black shadow-lg transition-all duration-200"
					>
						<i className="ri-close-line text-xl max-md:text-lg"></i>
					</button>
				</div>

				{/* Product name - 与空占位符标题区域保持相同尺寸 */}
				<div className="w-[180px] max-md:w-[130px] text-center h-[40px] max-md:h-[30px] flex items-center justify-center">
					<p className="text-[12px] max-md:text-[10px] text-black line-clamp-2 leading-relaxed font-medium">
						{productItem.name}
					</p>
				</div>
			</div>
		</div>
	);
}

export default React.memo(Index);

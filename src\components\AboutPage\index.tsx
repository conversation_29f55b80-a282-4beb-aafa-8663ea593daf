"use client";
import React, { useRef, useState, useEffect } from "react";
import Lightbox from "react-image-lightbox";
import "react-image-lightbox/style.css";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Controller, Navigation } from "swiper/modules";
import { containerVariants, itemVariants } from "@/lib/utils/util";
import { motion, useInView } from "framer-motion";
import { useTranslations } from "next-intl";
import BreadcrumbBanner from "@/components/AboutPage/breadcrumb-banner";
import Partner from "@/components/ZC/Partner";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";

function Index() {
  const t = useTranslations("about");
  const imageRef = useRef(null);
  const isImageInView = useInView(imageRef, { once: true, margin: "-100px 0px" });

  const imageRef2 = useRef(null);
  const isImage2InView = useInView(imageRef2, { once: true, margin: "-100px 0px" });

  // 定义动画变体
  const containerAnimation = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const itemAnimation = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.1, 0.25, 1]
      }
    }
  };

  const textAnimation = {
    hidden: { opacity: 0, x: -30 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.1, 0.25, 1]
      }
    }
  };

  // 图片动画变体 - 不使用opacity或初始隐藏
  const imageAnimation = {
    scale: isImageInView ? [0.7, 1.05, 1] : 1,
    rotateY: isImageInView ? [-15, 5, 0] : 0,
    y: isImageInView ? [30, -10, 0] : 0,
    transition: {
      duration: 1.5,
      times: [0, 0.7, 1],
      ease: "easeOut"
    }
  };

  // 第二个图片的动画变体 - 从右侧滑入
  const image2Animation = {
    x: isImage2InView ? [100, -20, 0] : 100,
    rotateZ: isImage2InView ? [5, -2, 0] : 5,
    scale: isImage2InView ? [0.9, 1.1, 1] : 0.9,
    transition: {
      duration: 1.8,
      times: [0, 0.6, 1],
      ease: "easeOut"
    }
  };

  return (
    <>
      <section>
        <div>
          <BreadcrumbBanner
            backgroundImage="/image/bread/about-us.png"
            title={t("title")}
            textPosition="left"
            className="h-[680px] max-xl:h-[450px] max-lg:h-[350px]"
          />
        </div>
        <motion.div
          className="container max-md:py-16 py-28"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerAnimation}
        >
          <div className="max-w-4xl mx-auto">
            <motion.h2
              className="text-4xl ib mb-12 uppercase"
              variants={itemAnimation}
            >
              {t("about_recell_title")}
            </motion.h2>

            <div className="space-y-6 text-lg">
              <motion.p
                className="text-[#393939] irs"
                variants={itemAnimation}
              >
                {t("universe_intro")}
              </motion.p>

              <motion.div
                className="mt-8 irs"
                variants={itemAnimation}
              >
                <p className="">
                  <span className="irs mr-2">{t("rc_intro")}</span>
                  <span className="ib mr-2"><em>{t("company_name")}</em></span>
                  <span className="irs">
                    (
                    <span className="ib mr-2"><em>{t("rc")}</em></span>
                    {t("for_short")}
                    ).
                  </span>
                </p>
                <div className="mt-4 space-y-4">
                  {/* <motion.p variants={itemAnimation}>
                    <span className="sans">—</span> {t("value_growth")}
                  </motion.p>
                  <motion.p variants={itemAnimation}>
                    <span className="sans">—</span> {t("value_boundaries")}
                  </motion.p> */}

                  <motion.p variants={itemAnimation}>
                    {t("our_mission")}
                  </motion.p>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>
        <div className="bg-[#eeeeee] max-md:py-20 py-32">
          <div className="container">
            <div className="flex flex-col lg:flex-row items-center gap-10 lg:gap-16">
              {/* 图片部分 - 使用视口触发动画但不使用初始隐藏 */}
              <div className="w-full lg:w-1/2 flex justify-center" style={{ perspective: "1000px" }}>
                <div
                  className="relative w-full max-w-lg 4 overflow-hidden group"
                >
                  <SEOOptimizedImage
                    src="/image/about/paddles.png"
                    alt="Pickleball Paddles"
                    width={600}
                    height={500}
                    priority
                    quality={100}
                    className="w-full object-contain transition-transform duration-500"
                  />
                  {/* 玻璃滑过效果 - 只用于图片 */}
                  {/* <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-[1000ms] ease-out">
                    <div className="h-full w-1/3 bg-gradient-to-r from-transparent via-white/30 to-transparent transform skew-x-12 blur-sm"></div>
                  </div> */}

                </div>
              </div>

              {/* 文字部分 - 使用动画 */}
              <motion.div
                className="w-full lg:w-1/2"
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true, margin: "-100px" }}
                variants={containerAnimation}
              >
                <motion.h2
                  className="text-xl  mb-8 uppercase"
                  variants={textAnimation}
                >
                  {t("values_title")}
                </motion.h2>

                <div className="space-y-4">
                  <motion.div className="" variants={textAnimation}>
                    <p className="irs">
                      <span className="text-[#393939]">{t("desgin")} </span>
                      <br />
                      <span className="text-black ib">{t("performance")}</span>
                      <br />
                      <span className="text-[#393939]">{t("performance_desc")} </span>
                    </p>
                  </motion.div>

                  <motion.div className="" variants={textAnimation}>
                    <p className="irs">
                      <span className="text-[#393939]">{t("deliver_promise")}</span>
                    </p>
                  </motion.div>

                  <motion.div className="" variants={textAnimation}>
                    <p className="irs">
                      <span className="text-[#393939]">{t("beyond_paddles")}</span>
                    </p>
                  </motion.div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>



        <div className="py-20">
          <div className="container">
            <div className="flex flex-col lg:flex-row items-center gap-10 lg:gap-16">
              {/* 左侧文字部分 */}
              <motion.div
                className="w-full lg:w-2/6 order-2 lg:order-1"
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true, margin: "-100px" }}
                variants={containerAnimation}
              >
                <motion.h2
                  className="text-xl mb-8 uppercase leading-tight ib"
                  variants={textAnimation}
                >
                  {t("no_perfect_title")}
                  {/* <br /> */}
                  {/* {t("just_right_fit")} */}
                </motion.h2>

                <div className="space-y-6 text-[#393939] irs">
                  <motion.p variants={textAnimation}>
                    {t("rally_to_daily_intro")}
                  </motion.p>
                  {/* 
                  <motion.p variants={textAnimation}>
                    {t("competitive_casual_desc")}
                  </motion.p>
                  <motion.p
                    className="text-black sans"
                    variants={textAnimation}
                  >
                    {t("join_us_text")}
                  </motion.p> */}
                </div>
              </motion.div>

              {/* 右侧图片部分 - 不使用初始隐藏动画 */}
              <div className="w-full lg:w-4/6 order-1 lg:order-2">
                <div
                  className="relative w-full group overflow-hidden"
                >
                  <SEOOptimizedImage
                    src="/image/about/players.png"
                    alt="Pickleball Players"
                    width={800}
                    height={600}
                    quality={100}
                    priority
                    className="w-full h-auto object-contain transition-transform duration-500"
                  />

                  {/* 玻璃滑过效果 */}
                  {/* <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-[1000ms] ease-out">
                    <div className="h-full w-1/3 bg-gradient-to-r from-transparent via-white/30 to-transparent transform skew-x-12 blur-sm"></div>
                  </div> */}
                </div>
              </div>
            </div>
          </div>
        </div>
        <Partner />
      </section>
    </>
  );
}

export default React.memo(Index);
"use client";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";

export default function RcDifference() {
    const t = useTranslations();
    const differenceItems = [
        {
            id: 1,
            imgSrc: "/image/home/<USER>",
            title: t("home.01ADAPTIVE"),
            description: t("home.01ADAPTIVE_DESC"),
        },
        {
            id: 2,
            imgSrc: "/image/home/<USER>",
            title: t("home.02GOLDENTICKET"),
            description: t("home.02GOLDENTICKET_DESC"),
        },
        {
            id: 3,
            imgSrc: "/image/home/<USER>",
            title: t("home.03AESTHETIC"),
            description: t("home.03AESTHETIC_DESC"),
        },
    ];

    return (
        <section className="relative w-full 2xl:max-h-[550px] overflow-hidden">
            {/* Background Image */}
            <SEOOptimizedImage
                src="/image/home/<USER>"
                alt="Players on a pickleball court, illustrating the RC Difference"
                width={1920}
                height={800}
                className="w-full max-1921xl:min-h-[550px] object-cover"
                priority
            />

            {/* Content */}
            <div className="absolute inset-0">
                <div className="relative mx-auto h-full container">
                    <div className="flex h-full flex-col justify-center">
                        <h2
                            className="mb-12 max-2xl:mb-6 text-center ib text-white  text-xl md:text-2xl 2xl:text-4xl uppercase"
                        >
                            {t("home.TheRCDifference")}
                        </h2>

                        <div className="grid grid-cols-1 gap-8 2xl:grid-cols-3 md:gap-8">
                            {differenceItems.map((item, index) => (
                                <motion.div
                                    key={item.title}
                                    className="flex items-center text-left 2xl:flex-col 2xl:items-center 2xl:text-center"
                                >
                                    <div className="relative h-20 w-20 flex-shrink-0 2xl:mb-6 2xl:h-[200px] 2xl:w-[200px] group overflow-hidden border-white border-[5px] max-md:border-[3px] rounded-full">
                                        <SEOOptimizedImage
                                            src={item.imgSrc}
                                            alt={item.title}
                                            width={500}
                                            height={500}
                                            priority
                                            quality={100}
                                            unoptimized
                                            className={`w-full h-full ${index === 2 ? "object-left" : ""}  object-cover transition-transform duration-700`}
                                        />
                                    </div>
                                    <div
                                        className="max-2xl:pl-4"
                                    >
                                        <h3 className="text-lg text-white md:text-xl 2xl:my-6 md:my-4 max-md:my-2 irs font-semibold">{item.title}</h3>
                                        <p className={`text-sm text-gray-300 2xl:max-w-[70%] ${item.id === 1 ? "2xl:max-w-[58%]" : ""} mx-auto  2xl:text-center md:mt-2  md:text-base irs !leading-relaxed`}>{item.description}</p>
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}
"use client";
import React from "react";
import { Modal } from "antd";
import { useTranslations, useLocale } from "next-intl";
import { ProductListItemFragment } from "@/gql/graphql";
import { filterCateImg } from "../Product/product-card";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { defaultLocale } from "@/config";
import { useProductStore } from "@/lib/store/product.store";
import useIsMobile from "@/lib/hooks/useIsMobile";
import { Link } from "@/navigation"
interface CompareModalProps {
  open: boolean;
  onClose: () => void;
  compareList: any[];
  onRemoveProduct: (id: string) => void;
}

const CompareModal: React.FC<CompareModalProps> = ({
  open,
  onClose,
  compareList,
  onRemoveProduct,
}) => {
  const t = useTranslations();
  const locale = useLocale();
  const { currencyUnit } = useProductStore();
  const isMobile = useIsMobile();
  console.log("compareList----",compareList);
  
  // 渲染价格
  const renderPrice = (price: any) => {
    if (!price?.start || !price?.stop) return "N/A";

    try {
      const startPrice = price.start.gross;
      const stopPrice = price.stop.gross;

      const isSamePrice = Number(startPrice.amount) === Number(stopPrice.amount);

      const formatPrice = (amount: any) => {
        return typeof amount === 'number'
          ? amount.toFixed(2)
          : typeof amount === 'string'
            ? parseFloat(amount).toFixed(2)
            : '0.00';
      };

      if (isSamePrice) {
        return `$ ${formatPrice(startPrice.amount)}`;
      } else {
        return `$ ${formatPrice(startPrice.amount)} - ${formatPrice(stopPrice.amount)}`;
      }
    } catch (error) {
      return "N/A";
    }
  };

  // 获取产品属性
  const getProductAttributes = (product: ProductListItemFragment) => {
    const attributes = product?.attributes?.map((item) => ({
      label: item.attribute?.translation?.name || item.attribute.name,
      value: item.values.map((i) => i.translation?.name || i.name).join(", "),
    })) || [];

    const variants = product?.variants?.map((variant) => ({
      name: variant?.translation?.name || variant?.name,
      sku: variant?.sku,
      weight: variant?.weight ? `${variant.weight.value} ${variant.weight.unit}` : null,
    })) || [];

    return { attributes, variants };
  };

  // 获取所有唯一的属性标签
  const getAllAttributeLabels = () => {
    // 按照设计图的顺序定义属性标签
    const orderedLabels = [
      "Color",
      "SKU",
      "Price (USD)",
      "Surface Material",
      "Core Material",
      "Finishing",
      "Printing",
      "Edge Guard",
      "Thickness",
      "Weight",
      "Length",
      "Width",
      "Grip Length",
      "Performance Type",
      "Power Rating",
      "Control Rating",
      "Spin Rating"
    ];

    return orderedLabels;
  };

  // 标签名称到 slug 的映射关系
  const getLabelToSlugMapping = () => {
    return {
      "Color": "color",
      "Surface Material": "surface-material",
      "Core Material": "core-technology",
      "Finishing": "finishing",
      "Printing": "printing",
      "Edge Guard": "edge-guard",
      "Thickness": "thickness",
      "Weight": "weight",
      "Length": "length",
      "Width": "width-inch",
      "Grip Length": "grip-length-inch",
      "Performance Type": "performance-type",
      "Power Rating": "power-rating",
      "Control Rating": "control-rating",
      "Spin Rating": "spin-rating"
    };
  };

  // 获取产品的特定属性值
  const getAttributeValue = (product: ProductListItemFragment, label: string) => {
    if (label === "Price (USD)") {
      return renderPrice(product.pricing?.priceRange);
    }

    if (label === "SKU") {
      return product.variants?.[0]?.sku || "-";
    }

    if (label === "Weight") {
      const weight = product.variants?.[0]?.weight;
      return weight ? `${weight.value}${weight.unit}` : "-";
    }

    // 特殊处理 Color 属性 - 从变体的 attributes 中获取
    if (label === "Color") {
      // 遍历所有变体，查找 Color 属性
      for (const variant of product.variants || []) {
        for (const attr of variant.attributes || []) {
          if (attr.attribute?.name === "Color" || attr.attribute?.translation?.name === "Color") {
            // 获取颜色值
            const colorValue = attr.values?.[0]?.name || attr.values?.[0]?.translation?.name;
            if (colorValue) {
              return colorValue;
            }
          }
        }
      }
      // 如果在变体中找不到，尝试从产品级别的 attributes 中查找
      const { attributes } = getProductAttributes(product);
      const colorAttr = attributes.find((a) => a.label === "Color");
      return colorAttr?.value || "-";
    }

    // 获取标签到 slug 的映射
    const labelToSlugMapping = getLabelToSlugMapping();
    const targetSlug = labelToSlugMapping[label];

    if (targetSlug) {
      // 根据 slug 查找对应的属性
      const matchedAttribute = product?.attributes?.find(attr =>
        attr.attribute?.slug === targetSlug
      );

      if (matchedAttribute) {
        // 获取属性值，优先使用翻译
        const values = matchedAttribute.values?.map(value =>
          value.translation?.name || value.name
        ).join(", ");
        return values || "-";
      }
    }

    // 如果没有找到对应的 slug，使用原来的方法作为后备
    const { attributes } = getProductAttributes(product);
    const attr = attributes.find((a) => a.label === label);
    return attr?.value || "-";
  };

  // 渲染特殊属性值（如评分）
  const renderAttributeValue = (product: ProductListItemFragment, label: string, value: string) => {
    // 如果是评分相关的属性，渲染进度条样式
    if (label.toLowerCase().includes('rating') || label.toLowerCase().includes('power') || label.toLowerCase().includes('control') || label.toLowerCase().includes('spin')) {
      // 尝试解析数值并渲染进度条
      const numValue = parseFloat(value);
      if (!isNaN(numValue) && numValue >= 0 && numValue <= 5) {
        return (
          <div className="w-full max-w-[120px]">
            <div className="flex items-center bg-[#e5e5e5] rounded-sm h-4 overflow-hidden">
              <div
                className="bg-black h-full transition-all duration-300"
                style={{ width: `${(numValue / 5) * 100}%` }}
              ></div>
              <div
                className="bg-[#e5e5e5] h-full flex-1"
              ></div>
            </div>
          </div>
        );
      }
    }

    // 特殊处理颜色属性
    if (label.toLowerCase() === 'color') {
      // 如果值是横杠，直接返回横杠
      if (value === '-') {
        return <span className="text-sm text-gray-600 text-center">-</span>;
      }

      // 检查是否是16进制颜色值
      const isHexColor = /^#[0-9A-Fa-f]{6}$/.test(value);

      if (isHexColor) {
        return (
          <div className="flex items-center justify-center">
            <div
              className="w-14 h-5"
              style={{ backgroundColor: value }}
            ></div>
          </div>
        );
      } else {
        // 如果不是16进制颜色，尝试将颜色名称转换为颜色
        const colorMap: { [key: string]: string } = {
          'red': '#FF0000',
          'blue': '#0000FF',
          'green': '#008000',
          'yellow': '#FFFF00',
          'black': '#000000',
          'white': '#FFFFFF',
          'gray': '#808080',
          'grey': '#808080',
          'orange': '#FFA500',
          'purple': '#800080',
          'pink': '#FFC0CB',
          'brown': '#A52A2A'
        };

        const colorValue = colorMap[value.toLowerCase()];
        if (colorValue) {
          return (
            <div className="flex items-center justify-center">
              <div
                className="w-14 h-5"
                style={{ backgroundColor: colorValue }}
              ></div>
            </div>
          );
        } else {
          // 如果无法匹配颜色，显示横杠
          return <span className="text-sm text-gray-600 text-center">-</span>;
        }
      }
    }

    return <span className="text-sm text-black text-center">{value}</span>;
  };

  const attributeLabels = getAllAttributeLabels();

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      width={isMobile ? "95%" : "90%"}
      //  top: 20
      style={{ maxWidth: "900px" }}
      className="compare-modal"
      title={
        <div className="flex items-center justify-center relative">
          <h3 className="text-[34px] mb-8 font-semibold text-center">Product Comparison</h3>
          <button
            onClick={onClose}
            className="absolute right-0 top-0 text-black hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>
      }
      closable={false}
    >
      <div className="max-h-[80vh]">
        {/* 使用单一表格结构，头部固定 */}
        <div className="border-r border-b border-gray-200">
          <div className="overflow-y-auto max-h-[70vh]">
            <table className="w-full table-fixed">
              <colgroup>
                <col className="w-40 md:w-48" />
                <col className="w-1/2" />
                <col className="w-1/2" />
              </colgroup>

              {/* 固定的产品信息头部 */}
              <thead className="sticky top-0 z-10">
                <tr className="bg-white border-b border-gray-200">
                  {/* 空白单元格 */}
                  <th className="border-r border-gray-200 pt-4"></th>

                  {/* 产品单元格 */}
                  {[0, 1].map((index) => {
                    const item = compareList[index];

                    if (!item?.node) {
                      return (
                        <th key={`empty-${index}`} className={`border-gray-200 p-4 pt-4 bg-white ${index === 0 ? 'border-r' : 'border-r'}`}>
                          <div className="flex flex-col items-center justify-center py-4">
                            {/* 空产品占位符 */}
                            <div className="w-16 h-16 md:w-20 md:h-20 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center mb-3">
                              {/* <i className="ri-add-line text-xl text-gray-400"></i> */}
                            </div>
                            <span className="text-xs text-gray-400 text-center">Add Product</span>
                          </div>
                        </th>
                      );
                    }

                    const product = item.node as ProductListItemFragment;
                    const imgs = filterCateImg(product.metadata);
                    const productName = locale === defaultLocale
                      ? product.name
                      : product.translation?.name || product.name;

                    return (
                      <th key={product.id} className={`border-gray-200  p-4 px-10 pt-4 relative bg-white ${index === 0 ? 'border-r' : 'border-r'} overflow-visible`}>
                        <div className="flex flex-col items-center justify-center">
                          {/* 产品图片 */}
                          <div className="w-full h-[220px] max-md:w-[130px] max-md:h-[160px] rounded-lg  border border-[#e5e5e5] mb-3 relative">
                            <SEOOptimizedImage
                              src={imgs[0]?.url || "/image/default-image.webp"}
                              alt={productName}
                              width={200}
                              height={200}
                              className="w-full h-full object-cover rounded-lg"
                            />
                            {/* 删除按钮 */}
                            <button
                              onClick={() => onRemoveProduct(product.id)}
                              className="absolute -top-2 -right-2 flex h-7 w-7 items-center justify-center rounded-full bg-white border  text-black shadow-lg transition-all duration-200 z-20"
                            >
                              <i className="ri-close-line text-lg !font-normal"></i>
                            </button>
                          </div>

                          {/* 产品名称 */}
                          <h3 className="text-xs ib !font-normal text-center line-clamp-2 mb-3 px-1 leading-tight">
                            {productName}
                          </h3>

                          {/* View Details 按钮 */}
                          <Link href={`/product/${product.slug}`} className="px-5 py-3 bg-[#83c000] text-white hover:text-white text-xs rounded-full !font-normal hover:bg-opacity-80 transition-colors">
                            View Details
                          </Link>
                        </div>
                      </th>
                    );
                  })}
                </tr>
              </thead>

              {/* 属性对比表格主体 */}
              <tbody>
                {attributeLabels.map((label, index) => (
                  <tr
                    key={label}
                    className={`${index % 2 === 0 ? "bg-white" : "bg-white"
                      } border-b border-[#e5e5e5] last:border-b-0`}
                  >
                    {/* 属性标签单元格 */}
                    <td className="border-r border-[#e5e5e5] px-4 py-3">
                      <span className="text-sm  !text-black">
                        {label}
                      </span>
                    </td>

                    {/* 产品属性值单元格 */}
                    {[0, 1].map((productIndex) => {
                      const item = compareList[productIndex];

                      if (!item?.node) {
                        return (
                          <td key={`empty-${label}-${productIndex}`} className={`border-[#e5e5e5] text-black px-4 py-3 text-center ${productIndex === 0 ? 'border-r' : 'border-r'}`}>
                            <span className="text-sm text-black">-</span>
                          </td>
                        );
                      }

                      const product = item.node as ProductListItemFragment;
                      const value = getAttributeValue(product, label);

                      return (
                        <td key={`${product.id}-${label}`} className={`border-[#e5e5e5] px-4 py-3 text-center ${productIndex === 0 ? 'border-r' : 'border-r'}`}>
                          {renderAttributeValue(product, label, value)}
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default CompareModal;

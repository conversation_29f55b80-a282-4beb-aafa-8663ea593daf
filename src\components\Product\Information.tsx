"use client";

import Skeleton from "react-loading-skeleton";
import Price from "@/components/Price/price";
import { BodyText } from "@/components/BodyText";
import Badge from "@/components/Badge";
import ProductAttributes from "./product-attributes";
import { formatPrice } from "@/lib/utils";
// import ProductCartBuyInquiry from "@/components/Product/product-cart-buy-inquiry";

import { Link } from "@/navigation";
// import ProductVariable from "@/components/Product/product-variable";
// import { useProductVariableStore } from "@/lib/store/product-variable.store";
import { useLocale, useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";
import { observeElementIntersection, productTranslationName } from "@/lib/utils/util";
import { debounce } from "@/lib/utils";
import { SocialShare } from "../SingleBlog/SocialShare";
import Rating from "react-rating";
import { useSelect } from "@react-three/drei";
import ProductCartBuyInquiry from "./product-cart-buy-inquiry";
import ProductInfo from "./ProductInfo";
import { defaultLocale } from "@/config";
import { Modal, Popconfirm } from "antd";
import { contactInfo, payarr } from "@/lib/contacts";
import { useModalInquiryContext } from "@/context/ModalInquiryContext";
import { useShoppingCartStore } from "@/lib/store/shoppingCart";
import { Collapse } from "antd";

type Props = {
	product: any | null;
};

export default function Information({ product }: Props) {
	let locale = useLocale();
	const [isClient, setIsClient] = useState(false);
	const [VariantActive, setVariantActive] = useState<any>(null);
	const [Variant, setVariant] = useState<any>(null);
	const [modal2Open, setModal2Open] = useState(false);
	const { setVariantActive3DName } = useModalInquiryContext();
	const t = useTranslations();
	const productSelectRef = useRef<HTMLDivElement | null>(null);
	const [isvariants, setIsVariants] = useState<boolean>(false);
	const [selectedVariantId, setSelectedVariantId] = useState<string>("");
	// 新增状态跟踪选中变体
	const [selectedVariant, setSelectedVariant] = useState<any>(null);
	const [costprice, setCostprice] = useState<string>("");
	// 添加评分状态
	const [rating, setRating] = useState(4.5);
	const { productCount } = useShoppingCartStore();
	// console.log(product);

	useEffect(() => {
		setIsClient(true);
		if (product?.variants?.length > 0) {
			setVariant(product.variants[0]);
		}
	}, [product]);

	// 自动同步选中变体数据
	// 修改useEffect处理未选中的情况
	useEffect(() => {
		console.log(selectedVariantId);
		if (product?.variants && selectedVariantId) {
			const variant = product.variants.find(v => v.id === selectedVariantId);
			setVariantActive(variant);
			setIsVariants(variant?.quantityAvailable > 0 || false);
			console.log("var------sss", variant);
			// 直接从选中的变体中获取 costprice
			if (variant?.attributes) {
				const costpriceAttr = variant.attributes.find(
					attr => ['cost', 'cost price', 'costprice'].includes(attr.attribute.name.trim().toLowerCase())
				);
				setCostprice(costpriceAttr?.values?.[0]?.name || costpriceAttr?.values?.[0]?.value || "");
			}
		} else {
			console.log("enter2222")
			// 当没有选中变体时重置状态
			setVariantActive(null);
			setIsVariants(false);
			// setCostprice("");  // 重置 costprice
		}
	}, [selectedVariantId, product?.variants]);

	const handlerAttribute = (item: any) => {
		setVariantActive(item);
		setVariant(item);
		console.log("item---", item);

		// 更新库存状态
		const updatedIsVariants = item.quantityAvailable > 0
		console.log("updatedIsVariants---", updatedIsVariants);

		setIsVariants(updatedIsVariants); // 用状态动态更新 isvariants
		if (item.attributes && item.attributes.length > 0) {
			const costprice = item.attributes.find((attr: any) => attr.attribute.name === "costprice");
			console.log("costprice---", costprice);
			setCostprice(costprice?.values?.[0]?.name || "");
		}
	};

	const description: any = product?.description ? JSON.parse(product?.description) : null;
	const descriptionJson: any = product?.translation?.descriptionJson
		? JSON.parse(product?.translation?.descriptionJson)
		: null;

	if (!isClient) {
		return <div className="min-h-[400px]"><Skeleton count={5} /></div>;
	}

	// console.log(VariantActive,'VariantActive');
	let productName = locale == defaultLocale ? productTranslationName(product?.name) : productTranslationName(product?.translation?.name) || productTranslationName(product?.name)

	return (
		<div className="relative max-w-[772px] flex-1">
			<div id="product-select" className="">
				{/* 产品名称 */}
				{product?.name ? (
					<h1 className="text-2xl md:text-3xl text-black leading-tight mb-4">
						{productName}
					</h1>
				) : (
					<>
						<Skeleton width={"100%"} height={40} />
						<Skeleton width={"100%"} height={40} />
					</>
				)}
				{/* 价格和库存状态 */}
				{product ? (
					VariantActive ? (
						<>
							{
								costprice ? <>
									<div className="flex items-center mt-2 mb-3">
										<span className="text-xl font-medium !text-main">
											${formatPrice(VariantActive?.pricing?.price?.gross?.amount?.toString() || "")}
										</span>
										<span className="ml-2 text-sm line-through text-gray-500">
											${isNaN(parseFloat(costprice)) ? costprice : formatPrice(costprice)}
										</span>
										<div className="ml-3">
											<div className={`flex items-center space-x-1 px-2 py-1 rounded-sm text-xs font-medium ${isvariants ? "bg-gray-100" : "bg-gray-50"}`}>
												<span className={`w-3 h-3 rounded-full flex items-center justify-center ${isvariants ? "bg-black text-white" : "bg-gray-400 text-white"}`}>
													{isvariants ? (
														<i className="ri-check-line text-[8px]"></i>
													) : (
														<i className="ri-close-line text-[8px]"></i>
													)}
												</span>
												<span className={isvariants ? "text-black" : "text-gray-500"}>{isvariants ? t("nav.IN STOCK") : t("nav.OUT OF STOCK")}</span>
											</div>
										</div>
									</div>
								</> : <div className="flex items-center gap-3 my-3">
									<div className="flex items-center">
										<Price price={(VariantActive?.pricing?.price.gross.amount || 0) * productCount} className="text-xl font-medium" />
									</div>

									<div className="flex items-center">
										<div className={`flex items-center space-x-1 px-2 py-1 rounded-sm text-xs font-medium ${isvariants ? "bg-gray-100" : "bg-gray-50"}`}>
											<span className={`w-3 h-3 rounded-full flex items-center justify-center ${isvariants ? "bg-black text-white" : "bg-gray-400 text-white"}`}>
												{isvariants ? (
													<i className="ri-check-line text-[8px]"></i>
												) : (
													<i className="ri-close-line text-[8px]"></i>
												)}
											</span>
											<span className={isvariants ? "text-black" : "text-gray-500"}>{isvariants ? t("nav.IN STOCK") : t("nav.OUT OF STOCK")}</span>
										</div>
									</div>
								</div>
							}
						</>
					) : (
						<div className="text-red-500 text-sm">
							{t("common.completeAttribute")}
						</div>
					)
				) : (
					<Skeleton width={120} height={30} />
				)}
				{/* 评分组件 */}
				{product && (
					<div className="flex items-center gap-2 mb-4">
						{/* @ts-ignore */}
						{/* <Rating
							initialRating={product?.rating || 5}
							readonly
							className="rss"
							emptySymbol={<i className="ri-star-line text-lg text-gray-300"></i>}
							fullSymbol={<i className="ri-star-fill text-lg text-main"></i>}
						/> */}
						{[1, 2, 3, 4, 5].map(i => (
							<i key={i} className={`ri-star-${i <= (product?.rating || 5) ? 'fill' : 'line'} text-black text-xl`}></i>
						))}
						<span className="text-xl text-gray-600">({product?.rating || 5})</span>
					</div>
				)}
				{/* 分隔线和描述 */}
				{/* {product && description?.blocks[1]?.data?.text && (
					<div className="py-4 border-t border-b border-gray-200">
						<BodyText
							innerHTML={
								locale === defaultLocale
									? description?.blocks[1]?.data?.text
									: descriptionJson?.blocks[1]?.data?.text || ""
							}
							className="text-base font-normal text-gray-700"
						></BodyText>
					</div>
				)} */}

				{/* 产品属性选择器 */}
				<div className="my-8">
					{product?.variants?.length > 0 && (
						<ProductAttributes
							variants={product?.variants}
							onChange={(variantId) => {
								setSelectedVariantId(variantId || null);
								// 同时更新库存状态
								const variant = product.variants.find(v => v.id === variantId);
								setIsVariants(variant?.quantityAvailable > 0);
							}}
							changeValue={handlerAttribute}
						/>
					)}
				</div>

				{/* 添加到购物车/购买/询价按钮 */}
				<div className="">
					<ProductCartBuyInquiry {...product} product={product} VariantActive={VariantActive} />
				</div>
				{/* 三个展开模块 */}
				<div className="space-y-2 mt-6 custom-panel-container">
					<Collapse expandIconPosition="right" className="custom-collapse" accordion bordered={false}>
						<Collapse.Panel header="Tech Specs" key="1" className="custom-panel mb-4">
							<div className="flex flex-col gap-3 p-4">
								{product?.attributes &&
									product?.attributes?.map((item, i) => {
										return (
											<div key={i} className="flex items-center gap-2 border-b border-gray-200 pb-2 last:border-0">
												<span className="font-medium text-gray-800">{item.attribute.name}:</span>
												<span className="text-gray-600">{item.values.map(v => v.name).join(', ')}</span>
											</div>
										);
									})}
							</div>
						</Collapse.Panel>
						<Collapse.Panel header="Design Philosophy" key="2" className="custom-panel mb-4">
							<BodyText
								innerHTML={
									locale === defaultLocale
										? description?.blocks[1]?.data?.text
										: descriptionJson?.blocks[1]?.data?.text || ""
								}
								className="text-base font-normal text-gray-700"
							></BodyText>
						</Collapse.Panel>
						{/* <Collapse.Panel header="Find Your Fit" key="3" className="custom-panel">
							444
						</Collapse.Panel> */}
					</Collapse>
				</div>
				{/* 配送与分享选项 */}
				<Share modal2Open={modal2Open} setModal2Open={setModal2Open} />

				{/* 商品的属性 */}
				{/* <ProductInfo product={product} /> */}
			</div>
		</div>
	);
}

function Share({ modal2Open, setModal2Open }) {
	const t = useTranslations('nav');

	return (
		<div className="space-y-4  pt-4">
			{/* 配送和分享按钮 */}
			<div className="flex flex-wrap gap-6 Information">
				<div
					onClick={() => setModal2Open(true)}
					className="flex items-center gap-x-2 cursor-pointer hover:text-gray-600 transition-colors"
				>
					<Car /> <span>{t('DeliveryReturn')}</span>
				</div>
				<Popconfirm
					icon={null}
					placement="topLeft"
					title={<SocialShare />}
					okText={null}
					cancelText={null}
					showCancel={false}
				>
					<div className="flex items-center gap-x-2 cursor-pointer hover:text-gray-600 transition-colors">
						<i className="ri-share-line text-xl"></i> <span>{t('Share')}</span>
					</div>
				</Popconfirm>
			</div>

			{/* 支付信息 */}
			<div className="flex flex-col sm:flex-row sm:items-center gap-3 py-2">
				<div className="flex items-center gap-2">
					<i className="ri-secure-payment-fill text-2xl text-green-600"></i>
					<span className="text-sm font-medium">{t('Guarantee Safe Checkout')}</span>
				</div>

				<div className="flex flex-wrap gap-3">
					{payarr.map(item => (
						<img key={item.img} src={item.img} className="h-[28px] w-auto object-contain" alt={item.alt} />
					))}
				</div>
			</div>

			{/* 配送信息模态框 */}
			<Modal
				title=""
				centered
				open={modal2Open}
				onOk={() => setModal2Open(false)}
				onCancel={() => setModal2Open(false)}
				footer={null}
			>
				<h3 className="mb-8 text-xl font-medium">{t('Help')}</h3>
				<p className="mb-4">{t('Give us')}</p>
				<p className="mb-4 flex items-center gap-2">
					<i className="ri-mail-line text-xl"></i>
					<span>{t('Email')}: {contactInfo.email}</span>
				</p>
				<p className="mb-4 flex items-center gap-2">
					<i className="ri-phone-line text-xl"></i>
					<span>{t('Phone')}: {contactInfo.phone}</span>
				</p>
			</Modal>
		</div>
	);
}

function Car() {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
			<path d="M12 16V14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
			<path d="M3 14V20C3 20.5523 3.44772 21 4 21H20C20.5523 21 21 20.5523 21 20V14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
			<path d="M14.5 6.5H9.5C8.94772 6.5 8.5 6.94772 8.5 7.5V10.5C8.5 11.0523 8.94772 11.5 9.5 11.5H14.5C15.0523 11.5 15.5 11.0523 15.5 10.5V7.5C15.5 6.94772 15.0523 6.5 14.5 6.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
			<path d="M8.5 9H4C3.44772 9 3 9.44772 3 10V14H21V10C21 9.44772 20.5523 9 20 9H15.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
			<path d="M7 4V6.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
			<path d="M17 4V6.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
		</svg>
	);
}
